import torch
from TTS.api import TTS
import os
import glob # Files ko list karne ke liye

# Environment variable for model loading
os.environ["TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD"] = "1"

# Paths define karein
input_text_file = "/media/hariswaqar/Data/tahir/voice_gen/lesson_7.txt"  # Aapki Arabic text file ka path
reference_audio_folder = "/media/hariswaqar/Data/tahir/voice_gen/ref_voices" # Aapke 100 reference audios wale folder ka path
output_base_dir = "/media/hariswaqar/Data/tahir/voice_gen/lesson_7_multi_ref" # Generated voices ke liye base output directory

# Base output directory banayen agar maujood nahi hai
os.makedirs(output_base_dir, exist_ok=True)

# TTS model initialize karein
device = "cuda" if torch.cuda.is_available() else "cpu"
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)

# Reference audio files ke paths hasil karein
# Yeh assume kar raha hai ke aapke reference folder mein .wav files hain
reference_audio_paths = sorted(glob.glob(os.path.join(reference_audio_folder, "*.wav")))

# Check karein ke 100 reference audios mile hain ya nahi
if len(reference_audio_paths) != 100:
    print(f"Warning: 100 reference audio files ki umeed thi, lekin {len(reference_audio_paths)} hi mile. Maujood files ke saath aage badh rahe hain.")
elif len(reference_audio_paths) == 0:
    print("Error: Reference audio folder mein koi .wav file nahi mili. Kripya path check karein ya files add karein.")
    exit() # Agar koi file nahi mili toh script rok den

# Text file se saari lines read karein
with open(input_text_file, "r", encoding="utf-8") as f:
    all_arabic_texts = [line.strip() for line in f.readlines()]

# Lines 21 se 30 tak select karein (Python mein index 0 se shuru hota hai, isliye 20 se 29)
# Agar file mein itni lines nahi hain toh yeh gracefully handle ho jayega
selected_arabic_texts = all_arabic_texts[0:108]

# Agar selected lines khaali hain
if not selected_arabic_texts:
    print(f"Warning: {input_text_file} mein lines 21 se 30 tak nahi mili. Koi audio generate nahi hogi.")

# Audio files generate karein
# Har select ki hui text line ke liye outer loop
for line_idx_offset, text_to_generate in enumerate(selected_arabic_texts):
    # Asli line number calculate karein (21, 22, ..., 30)
    original_line_number = 0 + line_idx_offset

    # Har original text line ke liye ek subfolder banayen
    line_output_dir = os.path.join(output_base_dir, f"line_{original_line_number}")
    os.makedirs(line_output_dir, exist_ok=True)

    print(f"\n--- Line {original_line_number} ke liye voices generate ho rahi hain ---")
    print(f"Text: '{text_to_generate}'")

    # Har reference audio ke liye inner loop
    for ref_idx, ref_audio_path in enumerate(reference_audio_paths, start=1):
        # Output file ka path banayen (e.g., Lesson4_voices_multi_ref/line_21/audio_ref_1.wav)
        output_path = os.path.join(line_output_dir, f"audio_ref_{ref_idx+100}.wav")

        try:
            tts.tts_to_file(
                text=text_to_generate,
                speaker_wav=ref_audio_path,  # Maujooda reference audio ka istemal karein
                language="ar",
                file_path=output_path
            )
            print(f"Generated: {output_path}")

        except Exception as e:
            print(f"Error: Line {original_line_number} ke liye reference audio {ref_idx} ({os.path.basename(ref_audio_path)}) ko process karte waqt masla: {str(e)}")

print("\nSaari generation mukammal ho gai hai!")