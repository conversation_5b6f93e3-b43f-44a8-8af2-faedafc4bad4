import os
import random
import IPython.display as ipd
import librosa
from deep_utils import warmup_cosine
from datasets import load_dataset, Audio
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification, TrainingArguments, Trainer
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score
import torch
from transformers import EarlyStoppingCallback
import pickle
from huggingface_hub import login

# Authenticate with Hugging Face
login(token='*************************************')  # Replace with your token

repo_name = "qaida-lesson1-classification"
branch_name = "updated_model_v2"

# Helper functions for pickling
def dump_pickle(file_path, data):
    with open(file_path, 'wb') as f:
        pickle.dump(data, f)

def load_pickle(file_path):
    with open(file_path, 'rb') as f:
        return pickle.load(f)

# Load feature extractor
feature_extractor = AutoFeatureExtractor.from_pretrained("facebook/wav2vec2-base")

# Load dataset
train_path = "/media/hariswaqar/Data/tahir/voice_gen/train_stratified.csv"
test_path = "/media/hariswaqar/Data/tahir/voice_gen/test_stratified.csv"
dataset = load_dataset('csv', data_files={'train': train_path, 'test': test_path})
dataset = dataset.cast_column("audio_path", Audio(sampling_rate=16_000))

# Label mappings
labels = set(dataset["train"]['label'])
label2id = {label: i for i, label in enumerate(labels)}
id2label = {i: label for label, i in label2id.items()}

# Save label mappings
os.makedirs("qaida_models/Lesson4/best", exist_ok=True)
dump_pickle("qaida_models/Lesson4/best/label2id.pkl", label2id)

# Preprocessing function
def preprocess_function(examples):
    audio_arrays = [x["array"] for x in examples["audio_path"]]
    inputs = feature_extractor(
        audio_arrays, sampling_rate=feature_extractor.sampling_rate, max_length=48000, truncation=True
    )
    inputs["label"] = [label2id[x] for x in examples["label"]]
    return inputs

# Apply preprocessing
encoded_dataset = dataset.map(preprocess_function, remove_columns=["audio_path"], batched=True)

# Metric function
def compute_metrics(p):
    predictions, labels = p
    predictions = np.argmax(predictions, axis=1)
    return {
        "accuracy": accuracy_score(labels, predictions),
        "f1-score": f1_score(labels, predictions, average="weighted"),
        "recall-score": recall_score(labels, predictions, average="weighted"),
        "precision-score": precision_score(labels, predictions, average="weighted"),
    }

# Early stopping
early_stopping = EarlyStoppingCallback(early_stopping_patience=5)

# Training setup
train_bs = 64
epochs = 100
lr = 5e-5
output_dir = "qaida_models/Lesson4"
total_steps = int((np.ceil(encoded_dataset["train"].num_rows / train_bs) * epochs))

# Load model
num_labels = len(id2label)
model = AutoModelForAudioClassification.from_pretrained(
    "facebook/wav2vec2-base", num_labels=num_labels, label2id=label2id, id2label=id2label
)

# Training arguments
training_args = TrainingArguments(
    output_dir=output_dir,
    eval_strategy="epoch",  # Changed from evaluation_strategy to eval_strategy
    save_strategy="epoch",
    num_train_epochs=epochs,
    report_to="tensorboard",
    load_best_model_at_end=True,
    save_total_limit=1,
    metric_for_best_model='loss',
    per_device_train_batch_size=train_bs,
    per_device_eval_batch_size=64,
    logging_steps=1,
    fp16=True,
    gradient_checkpointing=True,
    push_to_hub=False,
    hub_model_id=repo_name,
    hub_private_repo=False,
)

# Optimizer & Scheduler
optimizer = torch.optim.AdamW(model.parameters(), lr=lr)
scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, warmup_cosine(100, max_lr=lr, total_steps=total_steps, optimizer_lr=lr, min_lr=1e-6))

# Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=encoded_dataset["train"].with_format("torch"),
    eval_dataset=encoded_dataset["test"].with_format("torch"),
    tokenizer=feature_extractor,
    compute_metrics=compute_metrics,
    optimizers=(optimizer, scheduler),
    callbacks=[early_stopping]
)

# Train the model
trainer.train()
trainer.save_model(os.path.join(output_dir, "best"))
