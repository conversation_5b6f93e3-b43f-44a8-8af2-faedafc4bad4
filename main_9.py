import torch
from TTS.api import TTS
import os
from pathlib import Path

# Paths configuration
input_base_folder = '/media/hariswaqar/Data/tahir/voice_gen/lesson7'  # Base folder with label subfolders
ref_voices_folder = '/media/hariswaqar/Data/tahir/voice_gen/ref'  # Folder with reference voices
output_base_folder = '/media/hariswaqar/Data/tahir/voice_gen/lesson7_voices_multi_ref'  # Output base folder

# Ensure the output directory exists
os.makedirs(output_base_folder, exist_ok=True)

# Check for GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f'Using device: {device}')

print('Loading TTS model...')
api = TTS("voice_conversion_models/multilingual/vctk/freevc24").to(device)

# Get all reference voices
ref_voices = [f for f in os.listdir(ref_voices_folder) if f.endswith('.wav') or f.endswith('.mp3')]
print(f'Found {len(ref_voices)} reference voices')

# Get all label subfolders in the input directory
label_folders = [f for f in os.listdir(input_base_folder) if os.path.isdir(os.path.join(input_base_folder, f))]
print(f'Found {len(label_folders)} label folders to process')

# Process each label folder
for label_folder in label_folders:
    label_path = os.path.join(input_base_folder, label_folder)
    
    # Create corresponding output folder for this label
    output_label_folder = os.path.join(output_base_folder, label_folder)
    os.makedirs(output_label_folder, exist_ok=True)
    
    print(f'\n=== Processing label folder: {label_folder} ===')
    
    # Get all voice files in this label folder
    voice_files = [f for f in os.listdir(label_path) if f.endswith('.wav') or f.endswith('.mp3')]
    print(f'Found {len(voice_files)} voice files in {label_folder}')
    
    # Process each voice file in this label folder
    for voice_file in voice_files:
        input_voice_path = os.path.join(label_path, voice_file)
        
        # Create a subfolder for each input voice inside the label output folder
        voice_base_name = os.path.splitext(voice_file)[0]
        voice_output_folder = os.path.join(output_label_folder, voice_base_name)
        os.makedirs(voice_output_folder, exist_ok=True)
        
        print(f'\n  Processing voice file: {voice_file}')
        print(f'  Creating variations in: {voice_output_folder}')
        
        # Clone this voice with each reference voice
        for i, ref_filename in enumerate(ref_voices, 1):
            ref_voice_path = os.path.join(ref_voices_folder, ref_filename)
            
            # Create output filename
            ref_base_name = os.path.splitext(ref_filename)[0]
            output_filename = f"{voice_base_name}_cloned_with_{ref_base_name}.wav"
            output_path = os.path.join(voice_output_folder, output_filename)
            
            print(f'    [{i}/{len(ref_voices)}] Cloning with reference: {ref_filename}')
            
            try:
                api.voice_conversion_to_file(
                    source_wav=input_voice_path,
                    target_wav=ref_voice_path,
                    file_path=output_path
                )
            except Exception as e:
                print(f'    ERROR processing {voice_file} with {ref_filename}: {str(e)}')
                continue
    
    print(f'Completed processing label folder: {label_folder}')

print('\n=== PROCESSING COMPLETE ===')
print(f'All voice files have been processed successfully!')
print(f'Output saved to: {output_base_folder}')

# Print summary statistics
total_labels = len(label_folders)
total_voice_files = 0
total_variations = 0

for label_folder in label_folders:
    label_path = os.path.join(input_base_folder, label_folder)
    voice_files = [f for f in os.listdir(label_path) if f.endswith('.wav') or f.endswith('.mp3')]
    total_voice_files += len(voice_files)
    total_variations += len(voice_files) * len(ref_voices)

print(f'\nSUMMARY:')
print(f'- Total label folders processed: {total_labels}')
print(f'- Total original voice files processed: {total_voice_files}')
print(f'- Total voice variations generated: {total_variations}')
print(f'- Reference voices used: {len(ref_voices)}')
