# Quran Voice Generation Project

A text-to-speech (TTS) system specifically designed for generating high-quality Quranic recitations in Arabic using voice cloning technology.

## Overview

This project uses advanced deep learning models to generate natural-sounding Quranic recitations from Arabic text. It leverages the XTTS v2 model from Coqui TTS to clone voices from reference audio samples and apply them to Quranic verses. The system is capable of generating thousands of audio files with consistent voice characteristics while maintaining proper Arabic pronunciation.

## Features

- Voice cloning from reference audio samples
- Support for Arabic language and Quranic text
- Batch processing of large text files
- Multiple voice profile options (child, female voices)
- Automatic evaluation using Word Error Rate (WER) metrics
- GPU acceleration for faster processing

## Project Structure

```
voice-gen/
├── main_1.py                # TTS implementation with female voice
├── main_2.py                # TTS implementation with child voice
├── main_3.py                # TTS implementation with female voice 5
├── combine_lesson3_lines.py # Script to combine lines from lesson_3.txt
├── evaluation.py            # Script for evaluating audio quality using WER
├── txt_file_maker.ipynb     # Notebook for text file preparation
├── only_text_quran.txt      # Processed Quranic text for TTS input
├── quran.txt                # Original Quranic text with audio paths
├── lesson_3.txt             # Text for lesson 3
├── 1_ayats.txt              # Ayat text file
├── train.txt                # Training data text file
├── wer_file.txt             # File for WER evaluation
└── pyproject.toml           # Project dependencies
```

Note: Audio files and generated voice directories are excluded from the repository due to their large size.

## Requirements

- Python 3.10 or higher
- PyTorch 2.7.0 or higher
- jiwer 3.1.0 or higher (for WER calculation)
- Jupyter Notebook 7.4.2 or higher
- TTS (Coqui TTS library)
- CUDA-compatible GPU (recommended for faster processing)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/voice-gen.git
   cd voice-gen
   ```

2. Create a virtual environment and activate it:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install the dependencies:
   ```bash
   pip install -e .
   pip install TTS
   ```

## Usage

### Generating Audio with Different Voice Profiles

#### Child Voice
```bash
python main_1.py
```

#### Female Voice 4
```bash
python main_2.py
```

#### Female Voice 5
```bash
python main_3.py
```

### Text Preparation

The project includes a Jupyter notebook (`txt_file_maker.ipynb`) for preparing the input text files:

1. Filtering Quranic text by specific reciter
2. Removing audio paths to create clean text files
3. Checking for missing audio files due to token size limitations
4. Creating evaluation files for WER calculation

### Evaluation

To evaluate the quality of the generated audio using Word Error Rate (WER):

```bash
python evaluation.py
```

## How It Works

### Text-to-Speech Process

1. **Text Input**: The system reads Arabic text from `only_text_quran.txt`.
2. **Voice Cloning**: The XTTS v2 model analyzes a reference audio file to capture voice characteristics.
3. **Audio Generation**: The model generates audio for each line of text using the cloned voice profile.
4. **Output**: Audio files are saved in the specified output directory.

### Model Architecture

The project uses the XTTS v2 model from Coqui TTS, which is a multilingual, multi-speaker text-to-speech model that supports voice cloning. The model architecture includes:

- A text encoder that processes input text
- A voice encoder that extracts speaker characteristics from reference audio
- A decoder that generates mel-spectrograms
- A vocoder that converts mel-spectrograms to waveforms

### Evaluation Methodology

The evaluation script uses the Wav2Vec2 model from Hugging Face to transcribe the generated audio back to text. It then calculates:

- Word Error Rate (WER): Measures the accuracy of word recognition
- Character Error Rate (CER): Measures the accuracy at the character level

## Known Issues

- Some verses with more than 400 tokens may be skipped during processing (specifically lines 289 and 2822)
- The evaluation process requires a properly formatted manifest file with audio paths and corresponding text

## Future Improvements

- Fine-tuning the TTS model specifically for Quranic recitation
- Adding support for different recitation styles (tajweed rules)
- Implementing a web interface for easier interaction
- Expanding language support to include other languages

## License

[Specify your license here]

## Acknowledgments

- Coqui TTS for the XTTS v2 model
- Hugging Face for the Wav2Vec2 model used in evaluation
- [Add any other acknowledgments]