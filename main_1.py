import torch
from TTS.api import TTS
import os

# Set environment variable for model loading
# os.environ["TORCH_FORCE_WEIGHTS_ONLY_LOAD"] = "0"  # Corrected variable name
os.environ["TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD"] = "1" 

# Set paths
input_text_file = "/media/hariswaqar/Data/tahir/voice_gen/lesson_4.txt"  # Path to your Arabic text file
reference_audio = "/media/hariswaqar/Data/tahir/voice_gen/female.wav"     # Path to your reference speaker audio
output_dir = "Lesson4_voices"

# Create output directory
os.makedirs(output_dir, exist_ok=True)

# Initialize TTS
device = "cuda" if torch.cuda.is_available() else "cpu"
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)

# Read Arabic texts from file
with open(input_text_file, "r", encoding="utf-8") as f:
    arabic_texts = [line.strip() for line in f.readlines()]

# Generate audio files
for idx, text in enumerate(arabic_texts, start=1):
    output_path = os.path.join(output_dir, f"audio_{idx}.wav")

    try:
        tts.tts_to_file(
            text=text,
            speaker_wav=reference_audio,  # Using the same reference audio for all files
            language="ar",
            file_path=output_path
        )
        print(f"Generated: {output_path}")

    except Exception as e:
        print(f"Error processing line {idx}: {str(e)}")

print("Generation complete!")

# 289