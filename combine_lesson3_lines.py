#!/usr/bin/env python3
"""
Script to combine 10 lines from lesson_3.txt with tab spaces between them.
This creates a new file with each line containing 10 entries from the original file,
separated by tab characters.
"""

import os

def combine_lines_with_tabs(input_file, output_file, lines_per_group=10):
    """
    Combine lines from input file into groups, with tab spaces between them.
    
    Args:
        input_file (str): Path to the input file
        output_file (str): Path to the output file
        lines_per_group (int): Number of lines to combine in each group
    
    Returns:
        int: Number of groups created
    """
    # Read the input file
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f.readlines()]
    
    # Combine lines in groups with tab spaces
    combined_lines = []
    for i in range(0, len(lines), lines_per_group):
        # Get a group of lines
        group = lines[i:i+lines_per_group]
        # Join them with tab spaces
        combined_line = '\t'.join(group)
        combined_lines.append(combined_line)
    
    # Write the combined lines to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in combined_lines:
            f.write(line + '\n')
    
    return len(combined_lines)

def main():
    # Define the input and output file paths
    input_file = "lesson_3.txt"
    output_file = "combined_lesson3.txt"
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist.")
        return
    
    # Combine the lines
    num_groups = combine_lines_with_tabs(input_file, output_file)
    
    # Display the results
    print(f"Processing complete!")
    print(f"Combined lines from '{input_file}' into {num_groups} groups of 10 lines each.")
    print(f"Output saved to '{output_file}'")
    
    # Display a preview of the output file
    print("\nPreview of combined lines:")
    with open(output_file, 'r', encoding='utf-8') as f:
        preview_lines = f.readlines()[:3]  # Get first 3 lines for preview
        
    for i, line in enumerate(preview_lines):
        print(f"Group {i+1}: {line.strip()}")

if __name__ == "__main__":
    main()
