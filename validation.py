# import torch
# from transformers import <PERSON>FeatureExtractor, AutoModelForAudioClassification, Trainer, TrainingArguments
# from datasets import load_dataset, Audio
# import numpy as np
# from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score
# import pickle
# from huggingface_hub import login

# # Authenticate with Hugging Face Hub
# login(token='*************************************')  # Replace with your token

# repo_name = "lesson2_v1" # This will prompt you to enter your Hugging Face token

# # Load the feature extractor and the best-trained model
# feature_extractor = AutoFeatureExtractor.from_pretrained("facebook/wav2vec2-base")
# best_model_path = "qaida_models/Lesson1_v2/best"  # Replace with your best model path
# model = AutoModelForAudioClassification.from_pretrained(best_model_path)

# # Load your label2id mapping
# def load_pickle(file_path):
#     with open(file_path, 'rb') as f:
#         return pickle.load(f)

# label2id = load_pickle("qaida_models/Lesson1_v2/best/label2id.pkl")

# # Load and preprocess the test dataset
# test_path = "lessons_data/lesson3/val_valid.csv"
# dataset = load_dataset('csv', data_files={'test': test_path})
# dataset = dataset.cast_column("audio_path", Audio(sampling_rate=16_000))

# def preprocess_function(examples):
#     audio_arrays = [x["array"] for x in examples["audio_path"]]
#     inputs = feature_extractor(
#         audio_arrays, sampling_rate=feature_extractor.sampling_rate, max_length=16000, truncation=True
#     )
#     labels = []
#     for label in examples["label"]:
#         try:
#             label_id = int(label2id[label])
#             labels.append(label_id)
#         except KeyError:
#             print(f"Warning: Label '{label}' not found in label2id. Skipping this example.")
#             labels.append(-1)  # Use -1 or any other special value to indicate unknown labels

#     # Debugging: Print labels to check for out-of-range values
#     print("Labels being processed:", labels)

#     inputs["label"] = torch.tensor(labels, dtype=torch.long)
#     return inputs

# # Prepare the dataset for evaluation
# encoded_dataset = dataset.map(preprocess_function, remove_columns="audio_path", batched=True)
# encoded_dataset = encoded_dataset["test"].with_format("torch")

# # Define the compute metrics function
# def compute_metrics(p):
#     predictions, labels = p
#     predictions = np.argmax(predictions, axis=1)
#     acc = accuracy_score(labels, predictions)
#     f1 = f1_score(labels, predictions, average="weighted")
#     recall = recall_score(labels, predictions, average="weighted")
#     precision = precision_score(labels, predictions, average="weighted")
#     return {"accuracy": acc, "f1-score": f1, "recall-score": recall, "precision-score": precision}

# # Set up the TrainingArguments for evaluation
# training_args = TrainingArguments(
#     per_device_eval_batch_size=64,  # Adjust based on your GPU memory
#     output_dir="Lesson1_v2_val/validation_results",
#     logging_dir="Lesson1_v2_val/logs",
#     push_to_hub=False,  # Enable pushing to the Hub
#     hub_model_id=repo_name,  # Replace with your model ID
# )

# # Initialize the Trainer for evaluation
# trainer = Trainer(
#     model=model,
#     args=training_args,
#     eval_dataset=encoded_dataset,
#     tokenizer=feature_extractor,
#     compute_metrics=compute_metrics
# )

# # Perform evaluation
# eval_results = trainer.evaluate()
# print("Evaluation Results:", eval_results)

# # Push the model, logs, and evaluation results to the Hub
# # trainer.push_to_hub()















import torch
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification, Trainer, TrainingArguments
from datasets import load_dataset, Audio
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score
import pickle
from huggingface_hub import login

# Authenticate with Hugging Face Hub
login(token='*************************************')  # Replace with your token

repo_name = "haris-waqar/qaida-lesson7-classification"  # Replace with your actual repo name

# Load the feature extractor and the best-trained model from Hugging Face
feature_extractor = AutoFeatureExtractor.from_pretrained(repo_name)
model = AutoModelForAudioClassification.from_pretrained(repo_name)

# Load your label2id mapping from Hugging Face
def load_pickle_from_hub(repo_name, file_path):
    from huggingface_hub import hf_hub_download
    return pickle.load(open(hf_hub_download(repo_id=repo_name, filename=file_path), 'rb'))

label2id = load_pickle_from_hub(repo_name, "best/label2id.pkl")

# Load and preprocess the test dataset
test_path = "lessons_data/lesson3/val_valid.csv"
dataset = load_dataset('csv', data_files={'test': test_path})
dataset = dataset.cast_column("audio_path", Audio(sampling_rate=16_000))

def preprocess_function(examples):
    audio_arrays = [x["array"] for x in examples["audio_path"]]
    inputs = feature_extractor(
        audio_arrays, sampling_rate=feature_extractor.sampling_rate, max_length=16000, truncation=True
    )
    labels = []
    for label in examples["label"]:
        try:
            label_id = int(label2id[label])
            labels.append(label_id)
        except KeyError:
            print(f"Warning: Label '{label}' not found in label2id. Skipping this example.")
            labels.append(-1)  # Use -1 or any other special value to indicate unknown labels

    # Debugging: Print labels to check for out-of-range values
    print("Labels being processed:", labels)

    inputs["label"] = torch.tensor(labels, dtype=torch.long)
    return inputs

# Prepare the dataset for evaluation
encoded_dataset = dataset.map(preprocess_function, remove_columns="audio_path", batched=True)
encoded_dataset = encoded_dataset["test"].with_format("torch")

# Define the compute metrics function
def compute_metrics(p):
    predictions, labels = p
    predictions = np.argmax(predictions, axis=1)
    acc = accuracy_score(labels, predictions)
    f1 = f1_score(labels, predictions, average="weighted")
    recall = recall_score(labels, predictions, average="weighted")
    precision = precision_score(labels, predictions, average="weighted")
    return {"accuracy": acc, "f1-score": f1, "recall-score": recall, "precision-score": precision}

# Set up the TrainingArguments for evaluation
training_args = TrainingArguments(
    per_device_eval_batch_size=64,  # Adjust based on your GPU memory
    output_dir="Lesson1_v2_val/validation_results",
    logging_dir="Lesson1_v2_val/logs",
    push_to_hub=True,  # Enable pushing to the Hub
    hub_model_id=repo_name,  # Replace with your model ID
)

# Initialize the Trainer for evaluation
trainer = Trainer(
    model=model,
    args=training_args,
    eval_dataset=encoded_dataset,
    tokenizer=feature_extractor,
    compute_metrics=compute_metrics
)

# Perform evaluation
eval_results = trainer.evaluate()
print("Evaluation Results:", eval_results)

# Push the model, logs, and evaluation results to the Hub
# trainer.push_to_hub()
