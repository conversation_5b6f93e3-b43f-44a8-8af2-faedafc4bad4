import torch
from TTS.api import TTS
import os

# Paths configuration
input_folder = '/media/hariswaqar/Data/tahir/voice_gen/quran_surah'  # Folder containing input audio files
clone_wav_path = '/media/hariswaqar/Data/tahir/voice_gen/female.wav'  # Path to the clone reference audio file
output_folder = '/media/hariswaqar/Data/tahir/voice_gen/cloned_quran_surah_1'  # Folder to save the cloned voices

# Ensure the output directory exists
os.makedirs(output_folder, exist_ok=True)

# Check for GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"

print('Loading TTS model on GPU...')
api = TTS("voice_conversion_models/multilingual/vctk/freevc24").to(device)

# Iterate over all files in the input folder
for filename in os.listdir(input_folder):
    if filename.endswith('.wav') or filename.endswith('.mp3'):
        input_wav_path = os.path.join(input_folder, filename)
        output_filename = os.path.join(output_folder, f"cloned_{filename}")

        print(f'Generating audio for {filename}...')
        api.voice_conversion_to_file(
            source_wav=input_wav_path,
            target_wav=clone_wav_path,
            file_path=output_filename
        )

print('All audio files processed successfully!')
