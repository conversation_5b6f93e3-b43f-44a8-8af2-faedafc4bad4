import torch
from TTS.api import TTS
import os

# Paths configuration
input_folder = '/media/hariswaqar/Data/tahir/voice_gen/Qari_lesson_7'  # Folder with 10 input voices
ref_voices_folder = '/media/hariswaqar/Data/tahir/voice_gen/noon'  # Folder with 100 reference voices
output_folder = '/media/hariswaqar/Data/tahir/voice_gen/lesson_7_2_voices_multi_ref'  # Output folder

# Ensure the output directory exists
os.makedirs(output_folder, exist_ok=True)

# Check for GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"

print('Loading TTS model...')
api = TTS("voice_conversion_models/multilingual/vctk/freevc24").to(device)

# Get all reference voices
ref_voices = [f for f in os.listdir(ref_voices_folder) if f.endswith('.wav') or f.endswith('.mp3')]

# Iterate over all input voices
for input_filename in os.listdir(input_folder):

    if not (input_filename.endswith('.wav') or input_filename.endswith('.mp3')):
        continue

    input_wav_path = os.path.join(input_folder, input_filename)
    # Create a folder for each input voice inside output_folder
    input_base_name = os.path.splitext(input_filename)[0]
    input_output_folder = os.path.join(output_folder, input_base_name)
    os.makedirs(input_output_folder, exist_ok=True)

    print(f'\nProcessing input voice: {input_filename}')

    for ref_filename in ref_voices:
        ref_wav_path = os.path.join(ref_voices_folder, ref_filename)
        output_filename = f"{input_base_name}_cloned_with_{os.path.splitext(ref_filename)[0]}.wav"
        output_path = os.path.join(input_output_folder, output_filename)

        print(f'  Cloning with reference: {ref_filename}')
        api.voice_conversion_to_file(
            source_wav=input_wav_path,
            target_wav=ref_wav_path,
            file_path=output_path
        )

print('\nAll audio files processed successfully!')
