import os
import random
import tempfile
import sys

# Function to check if required libraries are installed
def check_dependencies():
    missing_deps = []
    try:
        import torch
    except ImportError:
        missing_deps.append("torch")

    try:
        from TTS.api import TTS
    except ImportError:
        missing_deps.append("TTS")

    try:
        import librosa
    except ImportError:
        missing_deps.append("librosa")

    try:
        import soundfile as sf
    except ImportError:
        missing_deps.append("soundfile")

    try:
        import numpy as np
    except ImportError:
        missing_deps.append("numpy")

    if missing_deps:
        print("Error: Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install them using pip:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    return True

# Check dependencies before proceeding
if not check_dependencies():
    sys.exit(1)

# Import required libraries now that we've checked they're installed
import torch
from TTS.api import TTS
import librosa
import soundfile as sf
import numpy as np

# =====================================================================
# CONFIGURATION SECTION - MODIFY THESE SETTINGS AS NEEDED
# =====================================================================

# Paths configuration
input_folder = '/media/hariswaqar/Data/tahir/voice_gen/Lesson 9'  # Folder with input voices
ref_voices_folder = '/media/hariswaqar/Data/tahir/voice_gen/ref_voices'  # Folder with reference voices
output_folder = '/media/hariswaqar/Data/tahir/voice_gen/lesson_9_cloned_10'  # Output folder
num_voices_to_generate = 10  # Number of voices to generate for each input

# Audio preprocessing settings - These help improve quality for elongated sounds
# Increase sample_rate for better quality (16000, 22050, or 24000)
sample_rate = 24000
# Increase top_db to trim more silence (20-30 is a good range)
silence_threshold = 20
# Percentile for dynamic range compression (higher = less compression)
compression_percentile = 95

# Ensure the output directory exists
os.makedirs(output_folder, exist_ok=True)

# =====================================================================
# AUDIO PREPROCESSING FUNCTION
# =====================================================================

def preprocess_audio(file_path):
    """
    Preprocess audio for better voice conversion.

    This function applies several techniques to improve voice conversion quality:
    1. Resampling to a consistent sample rate
    2. Trimming silence from beginning and end
    3. Normalizing audio levels
    4. Applying dynamic range compression to reduce extreme peaks

    These steps help significantly with elongated sounds like "laaaaaaammmmmm"
    by making the audio more consistent and reducing potential artifacts.
    """
    try:
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_path = temp_file.name
        temp_file.close()

        # Load audio with the configured sample rate
        y, sr = librosa.load(file_path, sr=sample_rate)

        # Trim silence using the configured threshold
        y, _ = librosa.effects.trim(y, top_db=silence_threshold)

        # Normalize audio levels
        y = librosa.util.normalize(y)

        # Apply dynamic range compression to reduce extreme peaks
        # This helps with elongated sounds by making them more consistent
        percentile_low = np.percentile(abs(y), 100 - compression_percentile)
        percentile_high = np.percentile(abs(y), compression_percentile)

        if percentile_high > percentile_low:
            # Clip extreme values
            y = np.clip(y, -percentile_high, percentile_high)
            # Normalize again after clipping
            y = librosa.util.normalize(y)

        # Save to temporary file
        sf.write(temp_path, y, sr)
        return temp_path
    except Exception as e:
        print(f"Error preprocessing audio: {str(e)}")
        return file_path  # Return original file if preprocessing fails

# Check for GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"

print('Loading TTS model...')
api = TTS("voice_conversion_models/multilingual/vctk/freevc24").to(device)

# Get all reference voices
ref_voices = [f for f in os.listdir(ref_voices_folder) if f.endswith('.wav') or f.endswith('.mp3')]
print(f'Found {len(ref_voices)} reference voices')

# Iterate over all input voices
for input_filename in os.listdir(input_folder):

    if not (input_filename.endswith('.wav') or input_filename.endswith('.mp3')):
        continue

    input_wav_path = os.path.join(input_folder, input_filename)
    # Create a folder for each input voice inside output_folder
    input_base_name = os.path.splitext(input_filename)[0]
    input_output_folder = os.path.join(output_folder, input_base_name)
    os.makedirs(input_output_folder, exist_ok=True)

    print(f'\nProcessing input voice: {input_filename}')

    # Randomly select 10 reference voices
    selected_ref_voices = random.sample(ref_voices, min(num_voices_to_generate, len(ref_voices)))

    print(f'Selected {len(selected_ref_voices)} random reference voices')

    for i, ref_filename in enumerate(selected_ref_voices, 1):
        ref_wav_path = os.path.join(ref_voices_folder, ref_filename)
        output_filename = f"{input_base_name}_cloned_{i}_with_{os.path.splitext(ref_filename)[0]}.wav"
        output_path = os.path.join(input_output_folder, output_filename)

        print(f'  [{i}/{num_voices_to_generate}] Cloning with reference: {ref_filename}')
        try:
            # Preprocess both input and reference audio
            print(f'    Preprocessing audio files...')
            processed_input = preprocess_audio(input_wav_path)
            processed_ref = preprocess_audio(ref_wav_path)

            # Use only the basic parameters that are supported by your TTS version
            api.voice_conversion_to_file(
                source_wav=processed_input,
                target_wav=processed_ref,
                file_path=output_path
            )
            print(f'    Saved to: {output_path}')

            # Clean up temporary files
            if processed_input != input_wav_path:
                os.unlink(processed_input)
            if processed_ref != ref_wav_path:
                os.unlink(processed_ref)

        except Exception as e:
            print(f'    Error processing {ref_filename}: {str(e)}')

print('\nAll audio files processed successfully!')

# =====================================================================
# NOTES ON IMPROVING VOICE CONVERSION QUALITY
# =====================================================================
#
# If you're still experiencing issues with elongated sounds like "laaaaaaammmmmm",
# try adjusting these parameters in the configuration section:
#
# 1. Increase sample_rate to 44100 for higher quality (requires more memory)
# 2. Adjust silence_threshold between 20-30 to trim more or less silence
# 3. Adjust compression_percentile:
#    - Lower values (80-90) apply more compression, which can help with elongated sounds
#    - Higher values (95-99) preserve more dynamics but may not fix the issue
#
# Additional tips:
# - Make sure your input audio files are clean and have minimal background noise
# - Try different reference voices - some voices convert better than others
# - For extremely problematic audio, consider splitting the audio at problematic points,
#   converting each part separately, and then concatenating the results
#
# The FreeVC24 model has limitations with certain types of audio, especially
# with elongated sounds. These preprocessing steps help mitigate the issues,
# but may not completely solve all cases.
