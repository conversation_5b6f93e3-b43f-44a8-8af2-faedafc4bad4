[project]
name = "voice-conversion"
version = "0.1.0"
description = "Voice conversion using Coqui TTS for multi-reference voice cloning"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "torch>=2.7.0",
    "torchaudio>=2.7.0",
    "TTS>=0.22.0",
    "numpy<2.0",
]

# Optional dependencies for development
[project.optional-dependencies]
dev = [
    "notebook>=7.4.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "src/__init__.py"