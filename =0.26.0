Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: accelerate in /home/<USER>/.local/lib/python3.10/site-packages (0.21.0)
Requirement already satisfied: numpy>=1.17 in /home/<USER>/.local/lib/python3.10/site-packages (from accelerate) (1.26.4)
Requirement already satisfied: packaging>=20.0 in /home/<USER>/.local/lib/python3.10/site-packages (from accelerate) (23.2)
Requirement already satisfied: psutil in /home/<USER>/.local/lib/python3.10/site-packages (from accelerate) (5.9.5)
Requirement already satisfied: pyyaml in /home/<USER>/.local/lib/python3.10/site-packages (from accelerate) (6.0.2)
Requirement already satisfied: torch>=1.10.0 in /home/<USER>/.local/lib/python3.10/site-packages (from accelerate) (2.6.0+cu126)
Requirement already satisfied: filelock in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (3.16.1)
Requirement already satisfied: typing-extensions>=4.10.0 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (4.13.2)
Requirement already satisfied: sympy==1.13.1 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (1.13.1)
Requirement already satisfied: networkx in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (2.8.8)
Requirement already satisfied: jinja2 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (3.1.3)
Requirement already satisfied: fsspec in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (2023.10.0)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.6.80)
Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (9.5.1.17)
Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.6.4.1)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (10.3.7.77)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (11.7.1.2)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (2.21.5)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (12.6.85)
Requirement already satisfied: triton==3.2.0 in /home/<USER>/.local/lib/python3.10/site-packages (from torch>=1.10.0->accelerate) (3.2.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/.local/lib/python3.10/site-packages (from sympy==1.13.1->torch>=1.10.0->accelerate) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/.local/lib/python3.10/site-packages (from jinja2->torch>=1.10.0->accelerate) (2.1.5)
