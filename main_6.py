import urllib.request
import urllib.error
import os
import sys
import time

base = "http://www.everyayah.com/data"
reciters = [
    # "Abdul<PERSON>amad_64kbps_QuranExplorer.Com",
    # "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>tal_64kbps",
    # "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_128kbps",
    # "<PERSON>_64kbps",
    # "<PERSON>_128kbps",
    # "<PERSON><PERSON><PERSON><PERSON><PERSON>_As-Sudais_64kbps",
    # "Abu_Bakr_Ash-Shaatree_64kbps",
    # "<PERSON>_<PERSON><PERSON>a_128kbps",
    # "<PERSON><PERSON><PERSON>_64kbps_QuranExplorer.Com",
    # "<PERSON>kram_AlAlaqimy_128kbps",
    # "Alafasy_64kbps",
    # "<PERSON>_Hajjaj_AlSuesy_128kbps",
    # "<PERSON>_Jaber_64kbps",
    # "Far<PERSON>_Abbad_64kbps",
    # "<PERSON><PERSON><PERSON>_40kbps",
    # "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_64kbps",
    # "Hudha<PERSON>_64kbps",
    # "Husary_64kbps",
    # "<PERSON>_32kbps",
    # "<PERSON><PERSON>_<PERSON>i_40kbps",
    # "<PERSON><PERSON><PERSON>d_<PERSON>laah_al-<PERSON><PERSON>ta<PERSON>e_192kbps",
    # "Maher_AlMuaiqly_64kbps",
    "Men<PERSON>i_32kbps",
    # "Minshawy_Murattal_128kbps",
    # "<PERSON>_al_Tablaway_64kbps",
    # "<PERSON>_<PERSON><PERSON>areem_128kbps",
    # "<PERSON>_Ayyoub_64kbps",
    # "<PERSON>_Jibreel_64kbps",
    # "Muhsin_Al_Qasim_192kbps",
    # "Mustafa_Ismail_48kbps",
    # "Nasser_Alqatami_128kbps",
    # "Parhizgar_48kbps",
    # "Sahl_Yassin_128kbps",
    # "Salaah_AbdulRahman_Bukhatir_128kbps",
    # "Salah_Al_Budair_128kbps",
    # "Saood_ash-Shuraym_64kbps",
    # "Yaser_Salamah_128kbps",
    # "Yasser_Ad-Dussary_128kbps",
]

# Number of ayahs in each surah
AYAHS_PER_SURAH = [
    7, 286, 200, 176, 120, 165, 206, 75, 129, 109, 123, 111, 43, 52, 99, 128,
    111, 110, 98, 135, 112, 78, 118, 64, 77, 227, 93, 88, 69, 60, 34, 30, 73,
    54, 45, 83, 182, 88, 75, 85, 54, 53, 89, 59, 37, 35, 38, 29, 18, 45, 60,
    49, 62, 55, 78, 96, 29, 22, 24, 13, 14, 11, 11, 18, 12, 12, 30, 52, 52,
    44, 28, 28, 20, 56, 40, 31, 50, 40, 46, 42, 29, 19, 36, 25, 22, 17, 19,
    26, 30, 20, 15, 21, 11, 8, 8, 19, 5, 8, 8, 11, 11, 8, 3, 9, 5, 4, 7, 3,
    6, 3, 5, 4, 5, 6
]

def download_ayah(surah, ayah):
    """Download a specific ayah from all reciters."""
    surah_str = f"{surah:03d}"
    ayah_str = f"{ayah:03d}"
    
    directory = f"wav/{surah_str}"
    if not os.path.exists(directory):
        os.makedirs(directory)

    directory += f"/{ayah_str}"
    if not os.path.exists(directory):
        os.makedirs(directory)

    for i, reciter in enumerate(reciters):
        download_url = f"{base}/{reciter}/{surah_str}{ayah_str}.mp3"
        output_file = f"{directory}/{surah_str}{ayah_str}_{i:02d}.mp3"
        print(f"[{i+1:2d}/{len(reciters):2d}] Downloading '{download_url}' to '{output_file}'")
        try:
            urllib.request.urlretrieve(download_url, output_file)
            # Add small delay to avoid overloading the server
            time.sleep(0.1)
        except (urllib.error.URLError, urllib.error.HTTPError) as e:
            print(f"Error downloading {download_url}: {e}")
        except OSError as e:
            print(f"File system error writing to {output_file}: {e}")

def download_all_quran():
    """Download all ayahs from all surahs."""
    print("Starting download of the entire Quran...")
    
    for surah in range(1, 115):  # Quran has 114 surahs
        print(f"\n--- Processing Surah {surah:03d} ---")
        for ayah in range(1, AYAHS_PER_SURAH[surah-1] + 1):
            download_ayah(surah, ayah)
            
    print("\nDownload complete!")

def download_surah(surah):
    """Download all ayahs of a specific surah."""
    if surah < 1 or surah > 114:
        print(f"Error: Surah number must be between 1 and 114. Got {surah}")
        return

    print(f"Downloading Surah {surah:03d} with {AYAHS_PER_SURAH[surah-1]} ayahs...")
    
    for ayah in range(1, AYAHS_PER_SURAH[surah-1] + 1):
        download_ayah(surah, ayah)
        
    print(f"\nSurah {surah:03d} download complete!")

if __name__ == '__main__':
    if len(sys.argv) == 1:
        print("Downloading the entire Quran...")
        download_all_quran()
    elif len(sys.argv) == 2:
        try:
            surah_num = int(sys.argv[1])
            download_surah(surah_num)
        except ValueError:
            print(f"Error: Surah number must be an integer. Got {sys.argv[1]}")
            sys.exit(1)
    elif len(sys.argv) < 3:
        print("Usage options:")
        print(f"  Download entire Quran: python {sys.argv[0]}")
        print(f"  Download specific surah: python {sys.argv[0]} <surah_number>")
        print(f"  Download specific ayah: python {sys.argv[0]} <surah_number> <ayah_number>")
        print(f"Example: python {sys.argv[0]} 1     # Download Al-Fatihah (complete)")
        print(f"Example: python {sys.argv[0]} 1 1   # Download Al-Fatihah, ayah 1")
        sys.exit(1)
    else:
        try:
            surah_num = int(sys.argv[1])
            ayah_num = int(sys.argv[2])
            download_ayah(surah_num, ayah_num)
        except ValueError:
            print("Error: Surah and ayah numbers must be integers.")
            sys.exit(1)